import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 简化的类型定义
interface ApkItem {
  id: string
  name: string
  packageName: string
  version: string
  description: string
  category: string
  size: number
  icon: string
  downloadUrl: string
  rating: number
  downloads: number
}

interface ApkCategory {
  id: string
  name: string
  count: number
}

interface ApkMarketData {
  apps: ApkItem[]
  lastUpdated: string
}

export const useApkMarketStore = defineStore('apkMarket', () => {
  // 状态
  const marketData = ref<ApkMarketData | null>(null)
  const categories = ref<ApkCategory[]>([])
  const isLoading = ref(false)
  const lastUpdated = ref<string | null>(null)
  const error = ref<string | null>(null)

  // 搜索和筛选
  const searchQuery = ref('')
  const selectedCategory = ref('')

  // 计算属性
  const filteredApps = computed(() => {
    if (!marketData.value?.apps) return []
    
    let apps = marketData.value.apps
    
    // 按分类筛选
    if (selectedCategory.value && selectedCategory.value !== 'all') {
      apps = apps.filter(app => app.category === selectedCategory.value)
    }
    
    // 按搜索关键词筛选
    if (searchQuery.value.trim()) {
      const query = searchQuery.value.toLowerCase()
      apps = apps.filter(app => 
        app.name.toLowerCase().includes(query) ||
        app.description.toLowerCase().includes(query)
      )
    }
    
    return apps
  })

  // 动作
  const fetchMarketData = async () => {
    isLoading.value = true
    error.value = null
    
    try {
      // 模拟API调用
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // 模拟数据
      marketData.value = {
        apps: [
          {
            id: '1',
            name: 'WeChat',
            packageName: 'com.tencent.mm',
            version: '8.0.32',
            description: '微信官方客户端',
            category: 'social',
            size: 150 * 1024 * 1024,
            icon: '/icons/wechat.png',
            downloadUrl: 'https://example.com/wechat.apk',
            rating: 4.8,
            downloads: 1000000000
          }
        ],
        lastUpdated: new Date().toISOString()
      }
      
      categories.value = [
        { id: 'all', name: '全部', count: marketData.value.apps.length },
        { id: 'social', name: '社交', count: 1 }
      ]
      
      lastUpdated.value = new Date().toISOString()
    } catch (err) {
      error.value = err instanceof Error ? err.message : '获取市场数据失败'
    } finally {
      isLoading.value = false
    }
  }

  const refreshMarketData = async () => {
    await fetchMarketData()
  }

  const setSearchQuery = (query: string) => {
    searchQuery.value = query
  }

  const setSelectedCategory = (category: string) => {
    selectedCategory.value = category
  }

  return {
    // 状态
    marketData,
    categories,
    isLoading,
    lastUpdated,
    error,
    searchQuery,
    selectedCategory,
    
    // 计算属性
    filteredApps,
    
    // 动作
    fetchMarketData,
    refreshMarketData,
    setSearchQuery,
    setSelectedCategory
  }
})

// 导出类型
export type { ApkItem, ApkCategory, ApkMarketData }
