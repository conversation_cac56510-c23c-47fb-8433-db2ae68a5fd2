import { lightTheme as naiveLightTheme, darkTheme as naiveDarkTheme } from 'naive-ui'
import type { GlobalTheme } from 'naive-ui'

// HOUT 应用的主色调
export const HOUT_PRIMARY_COLOR = '#00a0e9'

// 浅色主题配置 - 基于Naive UI默认主题，只覆盖主色调
export const lightTheme: GlobalTheme = {
  ...naiveLightTheme,
  common: {
    ...naiveLightTheme.common,
    primaryColor: HOUT_PRIMARY_COLOR,
    primaryColorHover: '#1890ff',
    primaryColorPressed: '#0078d4',
    primaryColorSuppl: '#40a9ff',

    // 信息色也使用主色调
    infoColor: HOUT_PRIMARY_COLOR,
    infoColorHover: '#1890ff',
    infoColorPressed: '#0078d4',
    infoColorSuppl: '#40a9ff'
  }
}

// 深色主题配置 - 基于Naive UI默认深色主题，只覆盖主色调
export const darkTheme: GlobalTheme = {
  ...naiveDarkTheme,
  common: {
    ...naiveDarkTheme.common,
    primaryColor: HOUT_PRIMARY_COLOR,
    primaryColorHover: '#40a9ff',
    primaryColorPressed: '#0078d4',
    primaryColorSuppl: '#1890ff',

    // 信息色也使用主色调
    infoColor: HOUT_PRIMARY_COLOR,
    infoColorHover: '#40a9ff',
    infoColorPressed: '#0078d4',
    infoColorSuppl: '#1890ff'
  }
}
