<template>
  <div class="tools-panel">
    <n-card title="工具集" :bordered="false">
      <n-result status="info" title="工具集" description="此功能正在开发中...">
        <template #icon>
          <n-icon size="64" color="#18a058">
            <Build />
          </n-icon>
        </template>
      </n-result>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { NCard, NResult, NIcon } from 'naive-ui'
import { Build } from '@vicons/ionicons5'
</script>

<style scoped>
.tools-panel {
  height: 100%;
}
</style>
