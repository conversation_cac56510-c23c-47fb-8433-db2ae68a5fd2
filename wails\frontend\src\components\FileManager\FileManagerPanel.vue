<template>
  <div class="file-manager-panel">
    <n-card title="文件管理" :bordered="false">
      <n-result status="info" title="文件管理" description="此功能正在开发中...">
        <template #icon>
          <n-icon size="64" color="#18a058">
            <Folder />
          </n-icon>
        </template>
      </n-result>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { NCard, NResult, NIcon } from 'naive-ui'
import { Folder } from '@vicons/ionicons5'
</script>

<style scoped>
.file-manager-panel {
  height: 100%;
}
</style>
