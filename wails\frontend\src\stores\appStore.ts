import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'
import { AppView, AppConfig, NotificationMessage } from "../types/app";

const defaultConfig: AppConfig = {
  theme: "light",
  language: "zh-CN",
  autoDetectDevices: true,
  scanInterval: 2000,
  logLevel: "info",
};

export const useAppStore = defineStore('app', () => {
  // 状态
  const isInitialized = ref(false)
  const config = reactive<AppConfig>({ ...defaultConfig })
  const currentView = ref<AppView>("device-info")
  const isLoading = ref(false)
  const error = ref<string | undefined>(undefined)
  const notifications = ref<NotificationMessage[]>([])

  // 动作
  const setCurrentView = (view: AppView) => {
    currentView.value = view
  }

  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  const setError = (err: string | undefined) => {
    error.value = err
  }

  const updateConfig = (configUpdates: Partial<AppConfig>) => {
    Object.assign(config, configUpdates)
    // 保存到localStorage
    localStorage.setItem('hout-app-config', JSON.stringify(config))
  }

  const addNotification = (notification: Omit<NotificationMessage, "id" | "timestamp">) => {
    const id = Date.now().toString()
    const newNotification: NotificationMessage = {
      ...notification,
      id,
      timestamp: new Date(),
    }

    notifications.value.push(newNotification)

    // 自动移除通知
    if (notification.autoClose !== false) {
      const duration = notification.duration || 5000
      setTimeout(() => {
        removeNotification(id)
      }, duration)
    }
  }

  const removeNotification = (id: string) => {
    const index = notifications.value.findIndex(n => n.id === id)
    if (index > -1) {
      notifications.value.splice(index, 1)
    }
  }

  const clearNotifications = () => {
    notifications.value = []
  }

  const initialize = () => {
    isInitialized.value = true
    // 从localStorage读取配置
    const savedConfig = localStorage.getItem('hout-app-config')
    if (savedConfig) {
      try {
        const parsedConfig = JSON.parse(savedConfig)
        Object.assign(config, parsedConfig)
      } catch (e) {
        console.warn('Failed to parse saved config:', e)
      }
    }
  }

  // 初始化
  initialize()

  return {
    isInitialized,
    config,
    currentView,
    isLoading,
    error,
    notifications,
    setCurrentView,
    setLoading,
    setError,
    updateConfig,
    addNotification,
    removeNotification,
    clearNotifications,
    initialize
  }
})
