<template>
  <div class="settings-panel">
    <n-card title="设置" :bordered="false">
      <n-result status="info" title="设置" description="此功能正在开发中...">
        <template #icon>
          <n-icon size="64" color="#18a058">
            <Settings />
          </n-icon>
        </template>
      </n-result>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { NCard, NResult, NIcon } from 'naive-ui'
import { Settings } from '@vicons/ionicons5'
</script>

<style scoped>
.settings-panel {
  height: 100%;
}
</style>
