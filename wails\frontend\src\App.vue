<template>
  <n-config-provider :theme="theme" :locale="zhCN">
    <n-global-style />
    <n-message-provider>
      <n-dialog-provider>
        <n-notification-provider>
          <div class="app-container">
            <!-- 加载状态 -->
            <div v-if="isLoading" class="loading-container">
              <n-spin size="large">
                <template #description>
                  正在加载 HOUT 工具箱...
                </template>
              </n-spin>
              <n-alert v-if="error" type="warning" style="margin-top: 16px; max-width: 400px">
                {{ error }}
              </n-alert>
            </div>

            <!-- 主应用界面 -->
            <div v-else class="main-app">
              <n-layout style="height: 100vh">
                <!-- 侧边栏 -->
                <n-layout-sider
                  bordered
                  collapse-mode="width"
                  :collapsed-width="64"
                  :width="240"
                  :collapsed="collapsed"
                  show-trigger
                  @collapse="collapsed = true"
                  @expand="collapsed = false"
                >
                  <div class="logo-container">
                    <n-avatar :size="40" src="/hout-icon.svg" />
                    <span v-if="!collapsed" class="logo-text">HOUT工具箱</span>
                  </div>
                  
                  <n-menu
                    v-model:value="currentView"
                    :collapsed="collapsed"
                    :collapsed-width="64"
                    :collapsed-icon-size="22"
                    :options="menuOptions"
                    @update:value="handleMenuSelect"
                  />
                </n-layout-sider>

                <n-layout>
                  <!-- 顶部工具栏 -->
                  <n-layout-header bordered style="height: 64px; padding: 0 24px">
                    <div class="header-content">
                      <div class="header-left">
                        <n-icon size="24" color="#18a058">
                          <Phone />
                        </n-icon>
                        <span class="header-title">{{ getViewTitle(currentView) }}</span>
                      </div>
                      
                      <div class="header-right">
                        <!-- 主题切换 -->
                        <n-switch
                          v-model:value="isDark"
                          @update:value="toggleTheme"
                        >
                          <template #checked>
                            🌙
                          </template>
                          <template #unchecked>
                            ☀️
                          </template>
                        </n-switch>
                        
                        <!-- 设备连接状态 -->
                        <n-badge :value="3" :max="99">
                          <n-button quaternary circle>
                            <template #icon>
                              <n-icon>
                                <Wifi />
                              </n-icon>
                            </template>
                          </n-button>
                        </n-badge>
                      </div>
                    </div>
                  </n-layout-header>

                  <!-- 主内容区 -->
                  <n-layout-content style="padding: 24px">
                    <component :is="currentComponent" />
                  </n-layout-content>
                </n-layout>
              </n-layout>
            </div>
          </div>
        </n-notification-provider>
      </n-dialog-provider>
    </n-message-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, h } from 'vue'
import {
  NConfigProvider,
  NLayout,
  NLayoutSider,
  NLayoutHeader,
  NLayoutContent,
  NMenu,
  NButton,
  NIcon,
  NAvatar,
  NSpin,
  NAlert,
  NSwitch,
  NBadge,
  NMessageProvider,
  NDialogProvider,
  NNotificationProvider,
  NGlobalStyle,
  zhCN,
  type MenuOption
} from 'naive-ui'
import {
  PhonePortrait as Phone,
  Folder,
  Code,
  Settings,
  Build,
  Power,
  Apps,
  Desktop,
  Wifi
} from '@vicons/ionicons5'
import { useThemeStore } from './stores/themeStore'
import { useAppStore } from './stores/appStore'
import type { AppView } from './types/app'
import { lightTheme, darkTheme } from './styles/theme'

// 导入组件
import DeviceInfoPanel from './components/DeviceInfo/DeviceInfoPanel.vue'
import FileManagerPanel from './components/FileManager/FileManagerPanel.vue'
import AdbToolsPanel from './components/AdbTools/AdbToolsPanel.vue'
import DeviceControlPanel from './components/DeviceControl/DeviceControlPanel.vue'
import AppManagerPanel from './components/AppManager/AppManagerPanel.vue'
import ScreenMirrorPanel from './components/ScreenMirror/ScreenMirrorPanel.vue'
import ToolsPanel from './components/Tools/ToolsPanel.vue'
import SettingsPanel from './components/Settings/SettingsPanel.vue'

// 状态管理
const themeStore = useThemeStore()
const appStore = useAppStore()

// 响应式数据
const isLoading = ref(true)
const error = ref<string | null>(null)
const collapsed = ref(false)

// 计算属性
const isDark = computed({
  get: () => themeStore.isDarkMode,
  set: (value: boolean) => themeStore.setDarkMode(value)
})

const theme = computed(() => isDark.value ? darkTheme : lightTheme)

const currentView = computed({
  get: () => appStore.currentView,
  set: (value: AppView) => appStore.setCurrentView(value)
})

// 菜单配置
const menuOptions = computed<MenuOption[]>(() => [
  {
    label: '设备信息',
    key: 'device-info',
    icon: () => h(NIcon, null, { default: () => h(Phone) })
  },
  {
    label: '文件管理',
    key: 'file-manager',
    icon: () => h(NIcon, null, { default: () => h(Folder) })
  },
  {
    label: 'ADB工具',
    key: 'adb-tools',
    icon: () => h(NIcon, null, { default: () => h(Code) })
  },
  {
    label: '设备控制',
    key: 'device-control',
    icon: () => h(NIcon, null, { default: () => h(Power) })
  },
  {
    label: '应用管理',
    key: 'app-manager',
    icon: () => h(NIcon, null, { default: () => h(Apps) })
  },
  {
    label: '屏幕镜像',
    key: 'screen-mirror',
    icon: () => h(NIcon, null, { default: () => h(Desktop) })
  },
  {
    label: '工具集',
    key: 'tools',
    icon: () => h(NIcon, null, { default: () => h(Build) })
  },
  {
    label: '设置',
    key: 'settings',
    icon: () => h(NIcon, null, { default: () => h(Settings) })
  }
])

// 组件映射
const componentMap = {
  'device-info': DeviceInfoPanel,
  'file-manager': FileManagerPanel,
  'adb-tools': AdbToolsPanel,
  'device-control': DeviceControlPanel,
  'app-manager': AppManagerPanel,
  'screen-mirror': ScreenMirrorPanel,
  'tools': ToolsPanel,
  'settings': SettingsPanel
}

const currentComponent = computed(() => {
  return componentMap[currentView.value] || DeviceInfoPanel
})

// 方法
const toggleTheme = () => {
  themeStore.toggleTheme()
}

const handleMenuSelect = (key: string) => {
  appStore.setCurrentView(key as AppView)
}

const getViewTitle = (view: AppView): string => {
  const titles = {
    'device-info': '设备信息',
    'file-manager': '文件管理',
    'adb-tools': 'ADB工具',
    'device-control': '设备控制',
    'app-manager': '应用管理',
    'screen-mirror': '屏幕镜像',
    'tools': '工具集',
    'settings': '设置'
  }
  return titles[view] || '设备信息'
}

// 生命周期
onMounted(() => {
  console.log('App.vue onMounted 开始')

  // 初始化主题
  themeStore.initTheme()

  // 初始化应用配置
  appStore.initialize()

  // 模拟初始化过程
  setTimeout(() => {
    isLoading.value = false
    console.log('HOUT Vue 应用初始化完成')
  }, 500) // 减少延迟时间
})
</script>

<style scoped>
.app-container {
  height: 100vh;
  overflow: hidden;
}

.loading-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 100vh;
  gap: 16px;
}

.logo-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
  border-bottom: 1px solid var(--n-border-color);
}

.logo-text {
  margin-left: 12px;
  font-weight: bold;
  font-size: 16px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 100%;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-title {
  font-size: 18px;
  font-weight: bold;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}
</style>
