@echo off
echo Building HOUT Wails application...

echo 1. Cleaning old build files...
if exist build rmdir /s /q build

echo 2. Running Wails build...
wails build
if %errorlevel% neq 0 (
    echo Build failed!
    exit /b 1
)

echo 3. Copying tools folder to build output...
xcopy tools build\bin\tools /E /I /Y
if %errorlevel% neq 0 (
    echo Failed to copy tools folder!
    exit /b 1
)

echo 4. Build completed!
echo Executable location: build\bin\hout-wails.exe
echo Included ADB tool: build\bin\tools\platform-tools\adb.exe

echo.
echo Build completed successfully!
