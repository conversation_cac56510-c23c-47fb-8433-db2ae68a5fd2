import { defineConfig } from "vite";
import vue from "@vitejs/plugin-vue";

// https://vitejs.dev/config/
export default defineConfig(async () => ({
  plugins: [vue()],

  // Vite options tailored for Wails development
  clearScreen: false,
  server: {
    port: 5173,
    strictPort: true,
    host: "localhost",
    cors: true,
  },
  build: {
    outDir: "dist",
    assetsDir: "assets",
    rollupOptions: {
      external: [],
    },
  },
}));
