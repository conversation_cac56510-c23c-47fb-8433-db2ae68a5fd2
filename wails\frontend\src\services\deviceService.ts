import { useCallback, useEffect, useRef } from "react";
import { useDeviceStore } from "../stores/deviceStore";
import { useAppStore } from "../stores/appStore";
import { DeviceInfo, DeviceProperties, CommandResult, InstalledApp, ApkInfo, BatchOperation, DeviceFile } from "../types/device";
import { WailsApi } from "./wailsApi";

export class DeviceService {
  private scanInterval: any = null;
  private isScanning = false;

  async scanDevices(): Promise<DeviceInfo[]> {
    try {
      const devices = await WailsApi.scanDevices();
      return devices;
    } catch (error) {
      console.error("Failed to scan devices:", error);
      throw error;
    }
  }

  async getDeviceInfo(serial: string): Promise<DeviceInfo> {
    try {
      const device = await WailsApi.getDeviceInfo(serial);
      return device;
    } catch (error) {
      console.error("Failed to get device info:", error);
      throw error;
    }
  }

  async getDeviceProperties(serial: string): Promise<DeviceProperties> {
    try {
      const properties = await WailsApi.getDeviceProperties(serial);
      return properties as DeviceProperties;
    } catch (error) {
      console.error("Failed to get device properties:", error);
      throw error;
    }
  }

  async executeAdbCommand(
    serial: string,
    command: string,
    args: string[] = [],
    timeout?: number
  ): Promise<CommandResult> {
    try {
      const fullCommand = [command, ...args];
      const result = await WailsApi.executeAdbCommand(serial, fullCommand);
      return result;
    } catch (error) {
      console.error("Failed to execute ADB command:", error);
      throw error;
    }
  }

  async rebootDevice(serial: string, mode: string): Promise<CommandResult> {
    try {
      const result = await WailsApi.rebootDevice(serial, mode);
      return result;
    } catch (error) {
      console.error("Failed to reboot device:", error);
      throw error;
    }
  }

  async installApk(serial: string, apkPath: string, replace = false): Promise<CommandResult> {
    try {
      const options = { replace };
      const result = await WailsApi.installApk(serial, apkPath, options);
      return result;
    } catch (error) {
      console.error("Failed to install APK:", error);
      throw error;
    }
  }

  async pushFile(serial: string, localPath: string, remotePath: string): Promise<CommandResult> {
    try {
      const result = await WailsApi.pushFile(serial, localPath, remotePath);
      return result;
    } catch (error) {
      console.error("Failed to push file:", error);
      throw error;
    }
  }

  async pullFile(serial: string, remotePath: string, localPath: string): Promise<CommandResult> {
    try {
      const result = await WailsApi.pullFile(serial, remotePath, localPath);
      return result;
    } catch (error) {
      console.error("Failed to pull file:", error);
      throw error;
    }
  }

  async listDeviceFiles(serial: string, path: string): Promise<DeviceFile[]> {
    try {
      const files = await WailsApi.listDeviceFiles(serial, path);
      return files;
    } catch (error) {
      console.error("Failed to list device files:", error);
      throw error;
    }
  }

  async getInstalledApps(serial: string, includeSystem: boolean = false): Promise<InstalledApp[]> {
    try {
      const apps = await WailsApi.getInstalledApps(serial, includeSystem);
      return apps;
    } catch (error) {
      console.error("Failed to get installed apps:", error);
      throw error;
    }
  }

  async uninstallApp(serial: string, packageName: string, keepData: boolean = false): Promise<CommandResult> {
    try {
      const result = await WailsApi.uninstallApp(serial, packageName);
      return result;
    } catch (error) {
      console.error("Failed to uninstall app:", error);
      throw error;
    }
  }

  async getApkInfo(apkPath: string): Promise<ApkInfo> {
    try {
      // 简化实现，返回基本信息
      const info: ApkInfo = {
        filePath: apkPath,
        packageName: "unknown",
        appName: "Unknown App",
        versionName: "1.0",
        versionCode: "1",
        permissions: [],
        features: [],
        fileSize: 0,
        isDebuggable: false,
        isTestOnly: false
      };
      return info;
    } catch (error) {
      console.error("Failed to get APK info:", error);
      throw error;
    }
  }

  async batchInstallApks(serial: string, apkPaths: string[], replaceExisting: boolean = false): Promise<BatchOperation> {
    try {
      // 简化实现，逐个安装
      const results: string[] = [];
      const errors: string[] = [];
      let success = 0;
      let failed = 0;

      for (const apkPath of apkPaths) {
        try {
          const options = { replace: replaceExisting };
          await WailsApi.installApk(serial, apkPath, options);
          results.push(`Successfully installed: ${apkPath}`);
          success++;
        } catch (error) {
          errors.push(`Failed to install ${apkPath}: ${error}`);
          failed++;
        }
      }

      const operation: BatchOperation = {
        id: `install-${Date.now()}`,
        operationType: 'install',
        totalItems: apkPaths.length,
        completedItems: success,
        failedItems: failed,
        status: failed > 0 ? 'failed' : 'completed',
        items: apkPaths.map((apk, index) => ({
          id: `item-${index}`,
          name: apk,
          status: index < success ? 'success' : 'failed'
        })),
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString()
      };
      return operation;
    } catch (error) {
      console.error("Failed to batch install APKs:", error);
      throw error;
    }
  }

  async batchUninstallApps(serial: string, packageNames: string[]): Promise<BatchOperation> {
    try {
      // 简化实现，逐个卸载
      const results: string[] = [];
      const errors: string[] = [];
      let success = 0;
      let failed = 0;

      for (const packageName of packageNames) {
        try {
          await WailsApi.uninstallApp(serial, packageName);
          results.push(`Successfully uninstalled: ${packageName}`);
          success++;
        } catch (error) {
          errors.push(`Failed to uninstall ${packageName}: ${error}`);
          failed++;
        }
      }

      const operation: BatchOperation = {
        id: `uninstall-${Date.now()}`,
        operationType: 'uninstall',
        totalItems: packageNames.length,
        completedItems: success,
        failedItems: failed,
        status: failed > 0 ? 'failed' : 'completed',
        items: packageNames.map((pkg, index) => ({
          id: `item-${index}`,
          name: pkg,
          status: index < success ? 'success' : 'failed'
        })),
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString()
      };
      return operation;
    } catch (error) {
      console.error("Failed to batch uninstall apps:", error);
      throw error;
    }
  }

  async checkAdbAvailability(): Promise<CommandResult> {
    try {
      const result = await WailsApi.checkAdbAvailability();
      return result;
    } catch (error) {
      console.error("Failed to check ADB availability:", error);
      throw error;
    }
  }

  async checkDeviceConnection(serial: string): Promise<CommandResult> {
    try {
      const connected = await WailsApi.checkDeviceConnection(serial);
      const result: CommandResult = {
        success: connected,
        output: connected ? "Device connected" : "Device not connected",
        error: connected ? "" : "Device not found or offline",
        exitCode: connected ? 0 : 1
      };
      return result;
    } catch (error) {
      console.error("Failed to check device connection:", error);
      throw error;
    }
  }

  async getDeviceConnectionInfo(serial: string): Promise<Record<string, unknown>> {
    try {
      const info = await WailsApi.getDeviceConnectionInfo(serial);
      return info;
    } catch (error) {
      console.error("Failed to get device connection info:", error);
      throw error;
    }
  }

  startScanning(interval = 2000): void {
    if (this.isScanning) return;

    this.isScanning = true;

    const scanDevicesInternal = async () => {
      try {
        const devices = await this.scanDevices();
        const deviceStore = useDeviceStore()
        deviceStore.setDevices(devices)
      } catch (error) {
        console.error("Device scan failed:", error);
      }
    };

    this.scanInterval = setInterval(scanDevicesInternal, interval);

    // 立即执行一次扫描
    scanDevicesInternal();
  }

  stopScanning(): void {
    if (this.scanInterval) {
      clearInterval(this.scanInterval);
      this.scanInterval = null;
    }
    this.isScanning = false;
  }

  // APK下载相关方法（简化实现）
  async downloadApk(url: string, fileName: string, isDirect: boolean): Promise<string> {
    try {
      // 简化实现 - 在实际项目中需要实现下载功能
      console.log(`Download APK: ${url} -> ${fileName} (direct: ${isDirect})`);
      throw new Error("APK下载功能暂未在Wails版本中实现");
    } catch (error) {
      // console.error("Failed to download APK:", error);
      throw error;
    }
  }

  async getDownloadSize(url: string, isDirect: boolean): Promise<number> {
    try {
      // 简化实现 - 返回默认大小
      console.log(`Get download size: ${url} (direct: ${isDirect})`);
      return 0; // 默认返回0，实际需要实现HTTP HEAD请求
    } catch (error) {
      // console.error("Failed to get download size:", error);
      throw error;
    }
  }
}

// 创建单例实例
export const deviceService = new DeviceService();

// React Hook for device service
export const useDeviceService = () => {
  const { setLoading, addNotification } = useAppStore();
  const scanningRef = useRef(false);

  const startScanning = useCallback(() => {
    if (scanningRef.current) return;
    
    scanningRef.current = true;
    setLoading(true);
    deviceService.startScanning();
    
    addNotification({
      type: "info",
      title: "设备扫描",
      message: "开始扫描连接的设备",
    });
  }, [setLoading, addNotification]);

  const stopScanning = useCallback(() => {
    if (!scanningRef.current) return;
    
    scanningRef.current = false;
    setLoading(false);
    deviceService.stopScanning();
  }, [setLoading]);

  const refreshDeviceInfo = useCallback(async (serial: string) => {
    try {
      const properties = await deviceService.getDeviceProperties(serial);
      const deviceStore = useDeviceStore()
      deviceStore.updateDevice(serial, { properties })
      
      addNotification({
        type: "success",
        title: "设备信息",
        message: "设备信息已更新",
      });
    } catch (error) {
      addNotification({
        type: "error",
        title: "设备信息",
        message: `获取设备信息失败: ${error}`,
      });
    }
  }, [addNotification]);

  useEffect(() => {
    return () => {
      stopScanning();
    };
  }, [stopScanning]);

  return {
    startScanning,
    stopScanning,
    refreshDeviceInfo,
    deviceService,
  };
};
