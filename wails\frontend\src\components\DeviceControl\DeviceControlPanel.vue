<template>
  <div class="device-control-panel">
    <n-card title="设备控制" :bordered="false">
      <n-result status="info" title="设备控制" description="此功能正在开发中...">
        <template #icon>
          <n-icon size="64" color="#18a058">
            <Power />
          </n-icon>
        </template>
      </n-result>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { NCard, NResult, NIcon } from 'naive-ui'
import { Power } from '@vicons/ionicons5'
</script>

<style scoped>
.device-control-panel {
  height: 100%;
}
</style>
