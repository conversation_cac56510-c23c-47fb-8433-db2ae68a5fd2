# HOUT Wails 版本

这是HOUT项目的Wails版本，从原始的Tauri版本转换而来。

## 项目结构

```
wails/
├── main.go              # 主程序入口
├── app.go               # 应用逻辑和API方法
├── go.mod               # Go模块依赖
├── wails.json           # Wails配置文件
├── README.md            # 说明文档
├── build.bat            # 构建脚本
├── dev.bat              # 开发脚本
├── tools/               # 内置工具
│   └── platform-tools/  # Android平台工具
│       ├── adb.exe      # Android Debug Bridge
│       ├── fastboot.exe # Fastboot工具
│       └── ...          # 其他工具
└── frontend/            # 前端代码 (Vue 3 + Naive UI)
    ├── src/             # Vue源代码
    ├── package.json     # 前端依赖
    ├── vite.config.ts   # Vite配置
    ├── tsconfig.json    # TypeScript配置
    └── index.html       # HTML模板
```

## ADB工具配置

本项目使用内置的ADB工具，无需系统安装Android SDK：

- **工具位置**: `tools/platform-tools/adb.exe`
- **版本**: Android Debug Bridge version 1.0.41 (Version 36.0.0)
- **自动路径解析**: 应用会自动查找并使用项目内置的ADB工具
- **构建时复制**: 构建时会自动将tools文件夹复制到输出目录

## 功能特性

### 已实现的API接口

- **设备管理**
  - `ScanDevices()` - 扫描连接的Android设备
  - `GetDeviceInfo(serial)` - 获取设备详细信息
  - `CheckDeviceConnection(serial)` - 检查设备连接状态
  - `GetDeviceConnectionInfo(serial)` - 获取设备连接信息
  - `GetDeviceProperties(serial)` - 获取设备属性
  - `GetDeviceFingerprint(serial)` - 获取设备指纹

- **ADB操作**
  - `ExecuteAdbCommand(serial, command)` - 执行ADB命令
  - `CheckAdbAvailability()` - 检查ADB可用性
  - `RebootDevice(serial, mode)` - 重启设备

- **应用管理**
  - `InstallApk(serial, apkPath, options)` - 安装APK文件
  - `GetInstalledApps(serial, includeSystem)` - 获取已安装应用列表
  - `UninstallApp(serial, packageName)` - 卸载应用

- **文件操作**
  - `PushFile(serial, localPath, remotePath)` - 推送文件到设备
  - `PullFile(serial, remotePath, localPath)` - 从设备拉取文件
  - `ListDeviceFiles(serial, path)` - 列出设备文件

- **配置管理**
  - `GetAppConfig()` - 获取应用配置
  - `SaveAppConfig(config)` - 保存应用配置

## 开发环境设置

### 前置要求

1. **Go 1.21+** - 后端开发语言
2. **Node.js 18+** - 前端开发环境
3. **Wails CLI** - Wails开发工具
4. **ADB工具** - Android调试桥

### 安装Wails CLI

```bash
go install github.com/wailsapp/wails/v2/cmd/wails@latest
```

### 安装依赖

```bash
# 进入wails目录
cd wails

# 安装Go依赖
go mod tidy

# 安装前端依赖
cd frontend
npm install
cd ..
```

## 构建和运行

### 开发模式

**推荐使用项目脚本（自动检查ADB工具）：**
```bash
# Windows
dev.bat

# 或手动运行
wails dev
```

这将启动开发服务器，支持热重载。脚本会自动检查ADB工具是否存在。

### 构建生产版本

**推荐使用项目脚本（自动复制ADB工具）：**
```bash
# Windows
build.bat

# 或手动构建
wails build
# 然后手动复制tools文件夹
xcopy tools build\bin\tools /E /I /Y
```

**其他平台版本：**
```bash
wails build -platform linux/amd64
wails build -platform darwin/amd64
# 注意：其他平台需要手动复制对应的ADB工具
```

## 与Tauri版本的差异

### 技术栈变化

| 组件 | Tauri版本 | Wails版本 |
|------|-----------|-----------|
| 后端语言 | Rust | Go |
| 前端框架 | React + Vite | React + Vite |
| 构建工具 | Tauri CLI | Wails CLI |
| API调用 | `invoke()` | `window.go.main.App.*` |

### API调用方式

**Tauri版本:**
```typescript
import { invoke } from "@tauri-apps/api/core";
const devices = await invoke<DeviceInfo[]>("scan_devices");
```

**Wails版本:**
```typescript
import { WailsApi } from "./services/wailsApi";
const devices = await WailsApi.scanDevices();
```

### 配置文件

- Tauri: `src-tauri/tauri.conf.json`
- Wails: `wails.json`

## 测试

### 运行前端测试

```bash
cd frontend
npm test
```

### 运行Go测试

```bash
go test ./...
```

## 部署

构建完成后，可执行文件将在 `build/bin/` 目录中生成。

### Windows
- `hout-wails.exe` - 主程序

### Linux
- `hout-wails` - 主程序

### macOS
- `hout-wails.app` - 应用包

## 故障排除

### 常见问题

1. **ADB工具问题**
   - 确保 `tools/platform-tools/adb.exe` 文件存在
   - 检查ADB工具是否有执行权限
   - 使用 `tools\platform-tools\adb.exe version` 测试ADB工具

2. **设备连接失败**
   - 检查USB调试是否开启
   - 确认设备驱动已安装

3. **构建失败**
   - 检查Go和Node.js版本
   - 确保所有依赖已安装

### 日志查看

开发模式下，日志会在终端中显示。生产版本的日志位置：

- Windows: `%APPDATA%/hout-wails/logs/`
- Linux: `~/.config/hout-wails/logs/`
- macOS: `~/Library/Application Support/hout-wails/logs/`

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 许可证

与原项目保持一致的许可证。
