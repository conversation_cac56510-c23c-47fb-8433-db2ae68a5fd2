{"name": "hout-wails-frontend-vue", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite --port 5173", "build": "vue-tsc && vite build", "lint": "eslint . --max-warnings 0", "preview": "vite preview", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"vue": "^3.4.0", "naive-ui": "^2.39.0", "@vicons/ionicons5": "^0.12.0", "pinia": "^2.1.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@types/node": "^24.1.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.28.0", "typescript": "~5.6.2", "vue-tsc": "^2.0.0", "vite": "^6.0.1", "rimraf": "^6.0.1"}}