/* HOUT 应用主题色彩变量 */
:root {
  --hout-primary-color: #00a0e9;
  --hout-primary-hover: #1890ff;
  --hout-primary-pressed: #0078d4;
  --hout-primary-light: #40a9ff;
  --hout-success-color: #52c41a;
  --hout-warning-color: #faad14;
  --hout-error-color: #ff4d4f;
}

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: "Segoe UI", "Microsoft YaHei", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, "Noto Sans", sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#app {
  height: 100%;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: var(--colorNeutralBackground2);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: var(--colorNeutralStroke1);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--colorNeutralStroke1Hover);
}

/* 紧凑模式下的额外样式 */
.compact-layout {
  --spacing-xs: 4px;
  --spacing-sm: 6px;
  --spacing-md: 8px;
  --spacing-lg: 12px;
  --spacing-xl: 16px;
}

/* 自定义动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 工具类 */
.fade-in {
  animation: fadeIn 0.3s ease-out;
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

.pulse {
  animation: pulse 2s infinite;
}

/* 无选择文本 */
.no-select {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 拖拽区域 */
.drag-region {
  -webkit-app-region: drag;
}

.no-drag {
  -webkit-app-region: no-drag;
}

/* 状态指示器 */
.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 8px;
}

.status-connected {
  background-color: var(--hout-success-color);
}

.status-disconnected {
  background-color: var(--hout-error-color);
}

.status-warning {
  background-color: var(--hout-warning-color);
}

/* HOUT 主题相关样式 */
.hout-primary {
  color: var(--hout-primary-color) !important;
}

.hout-primary-bg {
  background-color: var(--hout-primary-color) !important;
}

.hout-primary-border {
  border-color: var(--hout-primary-color) !important;
}

/* 设备状态指示器 */
.device-status-online {
  color: var(--hout-success-color);
}

.device-status-offline {
  color: var(--hout-error-color);
}

.device-status-unauthorized {
  color: var(--hout-warning-color);
}

/* 卡片阴影效果 */
.card-shadow {
  box-shadow: var(--shadow4);
  border: 1px solid var(--colorNeutralStroke2);
}

.card-shadow:hover {
  box-shadow: var(--shadow8);
  border-color: var(--colorNeutralStroke1);
}

/* 安全防护相关样式 */
.security-protected {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

/* 安全警告覆盖层 */
.security-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.9);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999999;
  color: white;
  font-size: 24px;
  text-align: center;
}

/* 禁用开发者工具时的样式 */
.devtools-disabled {
  pointer-events: none;
}

/* 安全测试区域样式 */
.security-test-area {
  border: 2px dashed var(--colorNeutralStroke2);
  padding: 16px;
  border-radius: 8px;
  background: var(--colorNeutralBackground2);
  text-align: center;
  margin: 8px 0;
  cursor: pointer;
  transition: all 0.2s ease;
}

.security-test-area:hover {
  border-color: var(--colorBrandStroke1);
  background: var(--colorBrandBackground2);
}

/* 防止右键菜单样式 */
.no-context-menu {
  -webkit-touch-callout: none;
  -webkit-user-select: none;
  -khtml-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* 安全状态指示器 */
.security-status-indicator {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: 4px;
}

.security-status-indicator::before {
  content: '';
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: var(--colorPaletteGreenBackground3);
  animation: pulse 2s infinite;
}

.security-status-indicator.disabled::before {
  background: var(--colorPaletteRedBackground3);
}

.security-status-indicator.warning::before {
  background: var(--colorPaletteYellowBackground3);
}
