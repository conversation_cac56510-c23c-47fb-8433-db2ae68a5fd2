import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  // 状态
  const isDarkMode = ref<boolean>(false)

  // 动作
  const setDarkMode = (dark: boolean) => {
    isDarkMode.value = dark
    // 保存到localStorage
    localStorage.setItem('hout-theme', dark ? 'dark' : 'light')
  }

  const toggleTheme = () => {
    setDarkMode(!isDarkMode.value)
  }

  const initTheme = () => {
    // 从localStorage读取主题设置
    const savedTheme = localStorage.getItem('hout-theme')
    if (savedTheme) {
      isDarkMode.value = savedTheme === 'dark'
    } else {
      // 默认跟随系统主题
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      isDarkMode.value = prefersDark
    }
  }

  // 初始化主题
  initTheme()

  return {
    isDarkMode,
    setDarkMode,
    toggleTheme,
    initTheme
  }
})
