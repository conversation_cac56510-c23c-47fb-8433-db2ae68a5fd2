package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
	"time"
)

// getAdbPath 获取项目内置的ADB工具路径
func getAdbPath() string {
	// 获取当前工作目录
	workDir, _ := os.Getwd()
	log.Printf("当前工作目录: %s", workDir)

	// 获取当前可执行文件的目录
	exePath, err := os.Executable()
	if err != nil {
		log.Printf("无法获取可执行文件路径: %v", err)
		// 如果获取失败，尝试使用相对路径
		relativePath := filepath.Join("tools", "platform-tools", "adb.exe")
		log.Printf("使用相对路径: %s", relativePath)
		return relativePath
	}

	// 获取可执行文件所在目录
	exeDir := filepath.Dir(exePath)
	log.Printf("可执行文件目录: %s", exeDir)

	// 构建ADB工具的路径
	adbPath := filepath.Join(exeDir, "tools", "platform-tools", "adb.exe")
	log.Printf("尝试路径1: %s", adbPath)

	// 检查文件是否存在
	if _, err := os.Stat(adbPath); err == nil {
		log.Printf("找到ADB工具: %s", adbPath)
		return adbPath
	}

	// 如果在可执行文件目录找不到，尝试在项目根目录查找
	projectRoot := filepath.Join(exeDir, "..")
	adbPath = filepath.Join(projectRoot, "tools", "platform-tools", "adb.exe")
	log.Printf("尝试路径2: %s", adbPath)
	if _, err := os.Stat(adbPath); err == nil {
		log.Printf("找到ADB工具: %s", adbPath)
		return adbPath
	}

	// 尝试从当前工作目录查找
	adbPath = filepath.Join(workDir, "tools", "platform-tools", "adb.exe")
	log.Printf("尝试路径3: %s", adbPath)
	if _, err := os.Stat(adbPath); err == nil {
		log.Printf("找到ADB工具: %s", adbPath)
		return adbPath
	}

	// 最后尝试相对路径
	relativePath := filepath.Join("tools", "platform-tools", "adb.exe")
	log.Printf("使用最终相对路径: %s", relativePath)
	return relativePath
}

// DeviceInfo 设备信息结构体
type DeviceInfo struct {
	Serial     string `json:"serial"`
	Model      string `json:"model"`
	Brand      string `json:"brand"`
	Version    string `json:"version"`
	Status     string `json:"status"`
	Mode       string `json:"mode"`
	IsOnline   bool   `json:"isOnline"`
	LastSeen   string `json:"lastSeen"`
	Properties map[string]string `json:"properties"`
}

// CommandResult 命令执行结果
type CommandResult struct {
	Success bool   `json:"success"`
	Output  string `json:"output"`
	Error   string `json:"error"`
	Code    int    `json:"code"`
}

// DeviceFile 设备文件信息
type DeviceFile struct {
	Name         string `json:"name"`
	Path         string `json:"path"`
	Size         int64  `json:"size"`
	IsDirectory  bool   `json:"isDirectory"`
	Permissions  string `json:"permissions"`
	LastModified string `json:"lastModified"`
}

// InstalledApp 已安装应用信息
type InstalledApp struct {
	PackageName string `json:"packageName"`
	AppName     string `json:"appName"`
	Version     string `json:"version"`
	VersionCode string `json:"versionCode"`
	IsSystem    bool   `json:"isSystem"`
	IsEnabled   bool   `json:"isEnabled"`
	InstallTime string `json:"installTime"`
	UpdateTime  string `json:"updateTime"`
	ApkPath     string `json:"apkPath"`
	DataDir     string `json:"dataDir"`
}

// ApkInfo APK文件信息
type ApkInfo struct {
	PackageName string `json:"packageName"`
	AppName     string `json:"appName"`
	Version     string `json:"version"`
	VersionCode string `json:"versionCode"`
	MinSdk      string `json:"minSdk"`
	TargetSdk   string `json:"targetSdk"`
	Size        int64  `json:"size"`
	Permissions []string `json:"permissions"`
	Activities  []string `json:"activities"`
	Services    []string `json:"services"`
	Receivers   []string `json:"receivers"`
}

// BatchOperation 批量操作结果
type BatchOperation struct {
	Total     int      `json:"total"`
	Success   int      `json:"success"`
	Failed    int      `json:"failed"`
	Results   []string `json:"results"`
	Errors    []string `json:"errors"`
	StartTime string   `json:"startTime"`
	EndTime   string   `json:"endTime"`
	Duration  string   `json:"duration"`
}

// ScreenMirrorConfig 屏幕镜像配置
type ScreenMirrorConfig struct {
	Quality    int    `json:"quality"`
	Bitrate    string `json:"bitrate"`
	MaxSize    int    `json:"maxSize"`
	Port       int    `json:"port"`
	ShowTouches bool  `json:"showTouches"`
	StayAwake  bool   `json:"stayAwake"`
	Fullscreen bool   `json:"fullscreen"`
}

// ScreenMirrorSession 屏幕镜像会话
type ScreenMirrorSession struct {
	DeviceSerial string                `json:"deviceSerial"`
	Config       ScreenMirrorConfig    `json:"config"`
	Status       string                `json:"status"`
	StartTime    string                `json:"startTime"`
	Port         int                   `json:"port"`
	ProcessID    int                   `json:"processId"`
}

// AppConfig 应用配置
type AppConfig struct {
	Theme           string            `json:"theme"`
	Language        string            `json:"language"`
	AutoScan        bool              `json:"autoScan"`
	ScanInterval    int               `json:"scanInterval"`
	DefaultPath     string            `json:"defaultPath"`
	AdbPath         string            `json:"adbPath"`
	FastbootPath    string            `json:"fastbootPath"`
	ScrcpyPath      string            `json:"scrcpyPath"`
	LogLevel        string            `json:"logLevel"`
	WindowSettings  map[string]interface{} `json:"windowSettings"`
	DeviceSettings  map[string]interface{} `json:"deviceSettings"`
	SecuritySettings map[string]interface{} `json:"securitySettings"`
}

// executeCommand 执行系统命令
func executeCommand(name string, args []string, timeout time.Duration) (*CommandResult, error) {
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()

	cmd := exec.CommandContext(ctx, name, args...)
	output, err := cmd.CombinedOutput()

	result := &CommandResult{
		Success: err == nil,
		Output:  string(output),
		Code:    0,
	}

	if err != nil {
		result.Error = err.Error()
		if exitError, ok := err.(*exec.ExitError); ok {
			result.Code = exitError.ExitCode()
		}
	}

	return result, nil
}

// parseDeviceList 解析设备列表
func parseDeviceList(output string) []map[string]string {
	var devices []map[string]string
	lines := strings.Split(output, "\n")
	
	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.Contains(line, "List of devices") {
			continue
		}
		
		parts := strings.Fields(line)
		if len(parts) >= 2 {
			device := map[string]string{
				"serial": parts[0],
				"status": parts[1],
			}
			devices = append(devices, device)
		}
	}
	
	return devices
}

// ScanDevices 扫描连接的设备
func (a *App) ScanDevices() ([]DeviceInfo, error) {
	var devices []DeviceInfo
	
	// 扫描ADB设备
	adbPath := getAdbPath()
	result, err := executeCommand(adbPath, []string{"devices"}, 10*time.Second)
	if err != nil {
		log.Printf("Failed to execute adb devices: %v", err)
	} else if result.Success {
		deviceList := parseDeviceList(result.Output)
		for _, device := range deviceList {
			deviceInfo := DeviceInfo{
				Serial:   device["serial"],
				Status:   device["status"],
				Mode:     "adb",
				IsOnline: device["status"] == "device",
				LastSeen: time.Now().Format(time.RFC3339),
			}
			devices = append(devices, deviceInfo)
		}
	}
	
	return devices, nil
}

// GetDeviceInfo 获取设备详细信息
func (a *App) GetDeviceInfo(serial string) (*DeviceInfo, error) {
	if serial == "" {
		return nil, fmt.Errorf("device serial cannot be empty")
	}
	
	device := &DeviceInfo{
		Serial:     serial,
		Properties: make(map[string]string),
	}
	
	// 获取设备属性
	adbPath := getAdbPath()
	result, err := executeCommand(adbPath, []string{"-s", serial, "shell", "getprop"}, 15*time.Second)
	if err != nil {
		return nil, fmt.Errorf("failed to get device properties: %v", err)
	}
	
	if result.Success {
		// 解析属性
		lines := strings.Split(result.Output, "\n")
		propRegex := regexp.MustCompile(`\[([^\]]+)\]: \[([^\]]*)\]`)
		
		for _, line := range lines {
			matches := propRegex.FindStringSubmatch(line)
			if len(matches) == 3 {
				key := matches[1]
				value := matches[2]
				device.Properties[key] = value
				
				// 提取常用属性
				switch key {
				case "ro.product.model":
					device.Model = value
				case "ro.product.brand":
					device.Brand = value
				case "ro.build.version.release":
					device.Version = value
				}
			}
		}
		
		device.Status = "device"
		device.Mode = "adb"
		device.IsOnline = true
		device.LastSeen = time.Now().Format(time.RFC3339)
	}
	
	return device, nil
}

// ExecuteAdbCommand 执行ADB命令
func (a *App) ExecuteAdbCommand(serial string, command []string) (*CommandResult, error) {
	if len(command) == 0 {
		return nil, fmt.Errorf("command cannot be empty")
	}

	args := []string{}
	if serial != "" {
		args = append(args, "-s", serial)
	}
	args = append(args, command...)

	adbPath := getAdbPath()
	result, err := executeCommand(adbPath, args, 30*time.Second)
	if err != nil {
		return nil, fmt.Errorf("failed to execute adb command: %v", err)
	}

	return result, nil
}

// RebootDevice 重启设备
func (a *App) RebootDevice(serial string, mode string) (*CommandResult, error) {
	if serial == "" {
		return nil, fmt.Errorf("device serial cannot be empty")
	}

	args := []string{"-s", serial, "reboot"}
	if mode != "" && mode != "system" {
		args = append(args, mode)
	}

	adbPath := getAdbPath()
	result, err := executeCommand(adbPath, args, 10*time.Second)
	if err != nil {
		return nil, fmt.Errorf("failed to reboot device: %v", err)
	}

	return result, nil
}

// InstallApk 安装APK文件
func (a *App) InstallApk(serial string, apkPath string, options map[string]bool) (*CommandResult, error) {
	if serial == "" {
		return nil, fmt.Errorf("device serial cannot be empty")
	}
	if apkPath == "" {
		return nil, fmt.Errorf("APK path cannot be empty")
	}

	// 检查文件是否存在
	if _, err := os.Stat(apkPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("APK file does not exist: %s", apkPath)
	}

	args := []string{"-s", serial, "install"}

	// 添加选项
	if options["replace"] {
		args = append(args, "-r")
	}
	if options["downgrade"] {
		args = append(args, "-d")
	}
	if options["grantPermissions"] {
		args = append(args, "-g")
	}

	args = append(args, apkPath)

	adbPath := getAdbPath()
	result, err := executeCommand(adbPath, args, 60*time.Second)
	if err != nil {
		return nil, fmt.Errorf("failed to install APK: %v", err)
	}

	return result, nil
}

// PushFile 推送文件到设备
func (a *App) PushFile(serial string, localPath string, remotePath string) (*CommandResult, error) {
	if serial == "" {
		return nil, fmt.Errorf("device serial cannot be empty")
	}
	if localPath == "" || remotePath == "" {
		return nil, fmt.Errorf("local path and remote path cannot be empty")
	}

	// 检查本地文件是否存在
	if _, err := os.Stat(localPath); os.IsNotExist(err) {
		return nil, fmt.Errorf("local file does not exist: %s", localPath)
	}

	args := []string{"-s", serial, "push", localPath, remotePath}

	adbPath := getAdbPath()
	result, err := executeCommand(adbPath, args, 120*time.Second)
	if err != nil {
		return nil, fmt.Errorf("failed to push file: %v", err)
	}

	return result, nil
}

// PullFile 从设备拉取文件
func (a *App) PullFile(serial string, remotePath string, localPath string) (*CommandResult, error) {
	if serial == "" {
		return nil, fmt.Errorf("device serial cannot be empty")
	}
	if localPath == "" || remotePath == "" {
		return nil, fmt.Errorf("local path and remote path cannot be empty")
	}

	// 确保本地目录存在
	localDir := filepath.Dir(localPath)
	if err := os.MkdirAll(localDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create local directory: %v", err)
	}

	args := []string{"-s", serial, "pull", remotePath, localPath}

	adbPath := getAdbPath()
	result, err := executeCommand(adbPath, args, 120*time.Second)
	if err != nil {
		return nil, fmt.Errorf("failed to pull file: %v", err)
	}

	return result, nil
}

// ListDeviceFiles 列出设备文件
func (a *App) ListDeviceFiles(serial string, path string) ([]DeviceFile, error) {
	if serial == "" {
		return nil, fmt.Errorf("device serial cannot be empty")
	}
	if path == "" {
		path = "/sdcard"
	}

	args := []string{"-s", serial, "shell", "ls", "-la", path}

	adbPath := getAdbPath()
	result, err := executeCommand(adbPath, args, 30*time.Second)
	if err != nil {
		return nil, fmt.Errorf("failed to list files: %v", err)
	}

	if !result.Success {
		return nil, fmt.Errorf("command failed: %s", result.Error)
	}

	var files []DeviceFile
	lines := strings.Split(result.Output, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "total") {
			continue
		}

		// 解析ls -la输出
		parts := strings.Fields(line)
		if len(parts) >= 8 {
			permissions := parts[0]
			sizeStr := parts[4]
			name := strings.Join(parts[8:], " ")

			if name == "." || name == ".." {
				continue
			}

			size, _ := strconv.ParseInt(sizeStr, 10, 64)
			isDirectory := strings.HasPrefix(permissions, "d")

			file := DeviceFile{
				Name:        name,
				Path:        filepath.Join(path, name),
				Size:        size,
				IsDirectory: isDirectory,
				Permissions: permissions,
			}

			files = append(files, file)
		}
	}

	return files, nil
}

// GetDeviceProperties 获取设备属性
func (a *App) GetDeviceProperties(serial string) (map[string]string, error) {
	if serial == "" {
		return nil, fmt.Errorf("device serial cannot be empty")
	}

	args := []string{"-s", serial, "shell", "getprop"}

	adbPath := getAdbPath()
	result, err := executeCommand(adbPath, args, 15*time.Second)
	if err != nil {
		return nil, fmt.Errorf("failed to get device properties: %v", err)
	}

	if !result.Success {
		return nil, fmt.Errorf("command failed: %s", result.Error)
	}

	properties := make(map[string]string)
	lines := strings.Split(result.Output, "\n")
	propRegex := regexp.MustCompile(`\[([^\]]+)\]: \[([^\]]*)\]`)

	for _, line := range lines {
		matches := propRegex.FindStringSubmatch(line)
		if len(matches) == 3 {
			properties[matches[1]] = matches[2]
		}
	}

	return properties, nil
}

// GetInstalledApps 获取已安装应用列表
func (a *App) GetInstalledApps(serial string, includeSystem bool) ([]InstalledApp, error) {
	if serial == "" {
		return nil, fmt.Errorf("device serial cannot be empty")
	}

	args := []string{"-s", serial, "shell", "pm", "list", "packages"}
	if !includeSystem {
		args = append(args, "-3") // 只显示第三方应用
	}

	adbPath := getAdbPath()
	result, err := executeCommand(adbPath, args, 30*time.Second)
	if err != nil {
		return nil, fmt.Errorf("failed to get installed apps: %v", err)
	}

	if !result.Success {
		return nil, fmt.Errorf("command failed: %s", result.Error)
	}

	var apps []InstalledApp
	lines := strings.Split(result.Output, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if strings.HasPrefix(line, "package:") {
			packageName := strings.TrimPrefix(line, "package:")

			app := InstalledApp{
				PackageName: packageName,
				IsSystem:    !strings.Contains(line, "-3"),
				IsEnabled:   true,
			}

			// 获取应用详细信息
			infoArgs := []string{"-s", serial, "shell", "dumpsys", "package", packageName}
			adbPath := getAdbPath()
			infoResult, err := executeCommand(adbPath, infoArgs, 10*time.Second)
			if err == nil && infoResult.Success {
				// 解析dumpsys输出获取更多信息
				app.AppName = packageName // 简化处理，实际应该解析label
			}

			apps = append(apps, app)
		}
	}

	return apps, nil
}

// UninstallApp 卸载应用
func (a *App) UninstallApp(serial string, packageName string) (*CommandResult, error) {
	if serial == "" {
		return nil, fmt.Errorf("device serial cannot be empty")
	}
	if packageName == "" {
		return nil, fmt.Errorf("package name cannot be empty")
	}

	args := []string{"-s", serial, "uninstall", packageName}

	adbPath := getAdbPath()
	result, err := executeCommand(adbPath, args, 30*time.Second)
	if err != nil {
		return nil, fmt.Errorf("failed to uninstall app: %v", err)
	}

	return result, nil
}

// CheckAdbAvailability 检查ADB可用性
func (a *App) CheckAdbAvailability() (*CommandResult, error) {
	adbPath := getAdbPath()
	result, err := executeCommand(adbPath, []string{"version"}, 5*time.Second)
	if err != nil {
		return nil, fmt.Errorf("ADB not available: %v", err)
	}

	return result, nil
}

// CheckDeviceConnection 检查设备连接状态
func (a *App) CheckDeviceConnection(serial string) (bool, error) {
	if serial == "" {
		return false, fmt.Errorf("device serial cannot be empty")
	}

	adbPath := getAdbPath()
	result, err := executeCommand(adbPath, []string{"-s", serial, "get-state"}, 5*time.Second)
	if err != nil {
		return false, err
	}

	return result.Success && strings.TrimSpace(result.Output) == "device", nil
}

// GetDeviceConnectionInfo 获取设备连接信息
func (a *App) GetDeviceConnectionInfo(serial string) (map[string]interface{}, error) {
	if serial == "" {
		return nil, fmt.Errorf("device serial cannot be empty")
	}

	info := make(map[string]interface{})

	// 检查连接状态
	connected, _ := a.CheckDeviceConnection(serial)
	info["connected"] = connected

	if connected {
		// 获取设备信息
		deviceInfo, err := a.GetDeviceInfo(serial)
		if err == nil {
			info["device"] = deviceInfo
		}

		// 获取连接类型
		adbPath := getAdbPath()
		result, err := executeCommand(adbPath, []string{"-s", serial, "shell", "getprop", "ro.adb.secure"}, 5*time.Second)
		if err == nil && result.Success {
			info["secure"] = strings.TrimSpace(result.Output) == "1"
		}
	}

	return info, nil
}

// GetAppConfig 获取应用配置
func (a *App) GetAppConfig() (*AppConfig, error) {
	// 默认配置
	config := &AppConfig{
		Theme:        "light",
		Language:     "zh-CN",
		AutoScan:     true,
		ScanInterval: 5,
		DefaultPath:  "/sdcard",
		LogLevel:     "info",
		WindowSettings:   make(map[string]interface{}),
		DeviceSettings:   make(map[string]interface{}),
		SecuritySettings: make(map[string]interface{}),
	}

	// 尝试从配置文件加载
	configPath := "config.json"
	if data, err := os.ReadFile(configPath); err == nil {
		json.Unmarshal(data, config)
	}

	return config, nil
}

// SaveAppConfig 保存应用配置
func (a *App) SaveAppConfig(config *AppConfig) error {
	if config == nil {
		return fmt.Errorf("config cannot be nil")
	}

	data, err := json.MarshalIndent(config, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %v", err)
	}

	configPath := "config.json"
	if err := os.WriteFile(configPath, data, 0644); err != nil {
		return fmt.Errorf("failed to save config: %v", err)
	}

	return nil
}

// GetDeviceFingerprint 获取设备指纹
func (a *App) GetDeviceFingerprint(serial string) (string, error) {
	if serial == "" {
		return "", fmt.Errorf("device serial cannot be empty")
	}

	adbPath := getAdbPath()
	result, err := executeCommand(adbPath, []string{"-s", serial, "shell", "getprop", "ro.build.fingerprint"}, 5*time.Second)
	if err != nil {
		return "", fmt.Errorf("failed to get device fingerprint: %v", err)
	}

	if !result.Success {
		return "", fmt.Errorf("command failed: %s", result.Error)
	}

	return strings.TrimSpace(result.Output), nil
}
