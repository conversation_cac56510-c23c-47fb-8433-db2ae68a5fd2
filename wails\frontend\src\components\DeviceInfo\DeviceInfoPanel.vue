<template>
  <div class="device-info-panel">
    <n-card title="设备信息" :bordered="false">
      <template #header-extra>
        <n-space>
          <n-button
            :type="autoRefreshEnabled ? 'default' : 'primary'"
            size="small"
            @click="toggleAutoRefresh"
          >
            {{ autoRefreshEnabled ? '停止自动刷新' : '启用自动刷新' }}
          </n-button>
          <n-button type="primary" :loading="isScanning" @click="scanDevices">
            <template #icon>
              <n-icon>
                <Refresh />
              </n-icon>
            </template>
            {{ isScanning ? '扫描中...' : '扫描设备' }}
          </n-button>
        </n-space>
      </template>

      <!-- 设备详细信息对话框 -->
      <n-modal v-model:show="showDeviceDetailModal" preset="dialog" title="设备详细信息">
        <div v-if="selectedDeviceDetail" class="device-detail-content">
          <n-descriptions :column="2" bordered>
            <n-descriptions-item label="设备序列号">
              {{ selectedDeviceDetail.serial }}
            </n-descriptions-item>
            <n-descriptions-item label="设备型号">
              {{ selectedDeviceDetail.model || '未知' }}
            </n-descriptions-item>
            <n-descriptions-item label="品牌">
              {{ selectedDeviceDetail.brand || '未知' }}
            </n-descriptions-item>
            <n-descriptions-item label="Android版本">
              {{ selectedDeviceDetail?.version || '未知' }}
            </n-descriptions-item>
            <n-descriptions-item label="连接状态">
              <n-tag :type="selectedDeviceDetail?.isOnline ? 'success' : 'error'">
                {{ selectedDeviceDetail?.isOnline ? '在线' : '离线' }}
              </n-tag>
            </n-descriptions-item>
            <n-descriptions-item label="最后检测时间">
              {{ selectedDeviceDetail?.lastSeen || '未知' }}
            </n-descriptions-item>
          </n-descriptions>

          <n-divider>设备属性</n-divider>

          <n-scrollbar style="max-height: 300px;">
            <n-descriptions v-if="selectedDeviceProperties" :column="1" bordered size="small">
              <n-descriptions-item
                v-for="(value, key) in selectedDeviceProperties"
                :key="key"
                :label="key"
              >
                {{ value }}
              </n-descriptions-item>
            </n-descriptions>
          </n-scrollbar>
        </div>
      </n-modal>

      <!-- 设备列表 -->
      <div v-if="devices.length > 0" class="device-list">
        <n-grid :cols="2" :x-gap="16" :y-gap="16">
          <n-grid-item v-for="device in devices" :key="device.serial">
            <n-card hoverable class="device-card">
              <template #header>
                <div class="device-header">
                  <n-space align="center">
                    <n-avatar :size="40" src="/icons/android.png" />
                    <div>
                      <n-text strong>{{ device.properties?.model || '未知设备' }}</n-text>
                      <br>
                      <n-text depth="3" style="font-size: 12px">{{ device.serial }}</n-text>
                    </div>
                  </n-space>
                  <n-tag
                    :type="getDeviceStatusType(device.mode, device.connected)"
                    :class="getDeviceStatusClass(device.mode, device.connected)"
                  >
                    <template #icon>
                      <div class="status-indicator" :class="getDeviceStatusClass(device.mode, device.connected)"></div>
                    </template>
                    {{ getDeviceStatusText(device.mode, device.connected) }}
                  </n-tag>
                </div>
              </template>

              <n-descriptions :column="1" size="small">
                <n-descriptions-item label="品牌">
                  {{ device.properties?.brand || '未知' }}
                </n-descriptions-item>
                <n-descriptions-item label="系统版本">
                  Android {{ device.properties?.androidVersion || '未知' }}
                </n-descriptions-item>
                <n-descriptions-item label="连接状态">
                  {{ device.connected ? '已连接' : '未连接' }}
                </n-descriptions-item>
              </n-descriptions>

              <template #action>
                <n-space>
                  <n-button size="small" type="primary" :disabled="!device.connected">
                    连接
                  </n-button>
                  <n-button size="small">详情</n-button>
                  <n-dropdown :options="deviceActions" @select="(key) => handleDeviceAction(key, device)">
                    <n-button size="small" quaternary>
                      <template #icon>
                        <n-icon>
                          <MoreHorizontal />
                        </n-icon>
                      </template>
                    </n-button>
                  </n-dropdown>
                </n-space>
              </template>
            </n-card>
          </n-grid-item>
        </n-grid>
      </div>

      <!-- 加载状态 -->
      <div v-else-if="isScanning" class="loading-state">
        <n-spin size="large">
          <template #description>
            <n-space vertical align="center">
              <n-text>正在扫描设备...</n-text>
              <n-text depth="3" style="font-size: 12px">
                请确保设备已连接并启用USB调试
              </n-text>
            </n-space>
          </template>
        </n-spin>
      </div>

      <!-- 空状态 -->
      <n-empty v-else description="暂无设备" style="margin: 60px 0">
        <template #icon>
          <n-icon size="64" color="#d9d9d9">
            <Phone />
          </n-icon>
        </template>
        <template #extra>
          <n-space vertical align="center">
            <n-text depth="3" style="margin-bottom: 16px">
              请确保：
            </n-text>
            <n-ul style="text-align: left; margin-bottom: 16px;">
              <n-li>设备已通过USB连接到电脑</n-li>
              <n-li>设备已启用"开发者选项"</n-li>
              <n-li>设备已启用"USB调试"</n-li>
              <n-li>已安装ADB工具并配置环境变量</n-li>
            </n-ul>
            <n-button type="primary" @click="scanDevices">重新扫描</n-button>
          </n-space>
        </template>
      </n-empty>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import {
  NCard,
  NButton,
  NIcon,
  NSpace,
  NGrid,
  NGridItem,
  NAvatar,
  NText,
  NTag,
  NDescriptions,
  NDescriptionsItem,
  NDropdown,
  NEmpty,
  NSpin,
  NUl,
  NLi,
  NModal,
  NDivider,
  NScrollbar,
  useMessage
} from 'naive-ui'
import {
  Refresh,
  PhonePortrait as Phone,
  EllipsisHorizontal as MoreHorizontal
} from '@vicons/ionicons5'
import { useDeviceStore } from '../../stores/deviceStore'
import type { DeviceInfo, DeviceMode, GoDeviceInfo } from '../../types/device'

// 状态管理
const deviceStore = useDeviceStore()
const message = useMessage()

// 响应式数据
const isScanning = ref(false)
const devices = ref<DeviceInfo[]>([])
const showDeviceDetailModal = ref(false)
const selectedDeviceDetail = ref<any>(null)
const selectedDeviceProperties = ref<Record<string, string> | null>(null)

// 设备操作菜单
const deviceActions = [
  {
    label: '重启设备',
    key: 'reboot'
  },
  {
    label: '断开连接',
    key: 'disconnect'
  },
  {
    label: '设备信息',
    key: 'info'
  },
  {
    type: 'divider'
  },
  {
    label: '删除设备',
    key: 'remove'
  }
]

// 方法
const scanDevices = async () => {
  isScanning.value = true
  try {
    console.log('开始扫描设备...')

    // 检查ADB可用性
    try {
      await window.go.main.App.CheckAdbAvailability()
      console.log('ADB可用')
    } catch (adbError) {
      console.error('ADB不可用:', adbError)
      message.error('ADB不可用，请确保已安装Android SDK并配置环境变量')
      return
    }

    // 扫描设备
    const scannedDevices: any[] = await window.go.main.App.ScanDevices()
    console.log('扫描到的设备:', scannedDevices)

    // 转换设备数据格式
    const deviceList: DeviceInfo[] = []

    for (const device of scannedDevices) {
      try {
        // 获取设备详细信息
        const deviceInfo: any = await window.go.main.App.GetDeviceInfo(device.serial)
        console.log(`设备 ${device.serial} 详细信息:`, deviceInfo)

        const deviceData: DeviceInfo = {
          serial: device.serial,
          mode: mapDeviceStatus((device as any).status),
          connected: (device as any).isOnline || (device as any).status === 'device',
          properties: {
            model: (device as any).model || '未知设备',
            brand: (device as any).brand || '未知品牌',
            androidVersion: (device as any).version || '未知版本',
            marketName: deviceInfo.properties?.['ro.product.marketname'],
            productName: deviceInfo.properties?.['ro.product.name'],
            manufacturer: deviceInfo.properties?.['ro.product.manufacturer'],
            buildId: deviceInfo.properties?.['ro.build.id'],
            buildFingerprint: deviceInfo.properties?.['ro.build.fingerprint'],
            cpuAbi: deviceInfo.properties?.['ro.product.cpu.abi'],
            lcdDensity: deviceInfo.properties?.['ro.sf.lcd_density'],
            locale: deviceInfo.properties?.['ro.product.locale'],
            secure: deviceInfo.properties?.['ro.secure'] === '1',
            adbSecure: deviceInfo.properties?.['ro.adb.secure'] === '1',
            debuggable: deviceInfo.properties?.['ro.debuggable'] === '1'
          },
          lastSeen: new Date()
        }

        deviceList.push(deviceData)
      } catch (detailError) {
        console.error(`获取设备 ${device.serial} 详细信息失败:`, detailError)

        // 即使获取详细信息失败，也添加基本设备信息
        const basicDevice: DeviceInfo = {
          serial: device.serial,
          mode: mapDeviceStatus((device as any).status),
          connected: (device as any).isOnline || (device as any).status === 'device',
          properties: {
            model: '未知设备',
            brand: '未知品牌',
            androidVersion: '未知版本'
          },
          lastSeen: new Date()
        }

        deviceList.push(basicDevice)
      }
    }

    devices.value = deviceList

    if (deviceList.length > 0) {
      message.success(`发现 ${deviceList.length} 个设备`)
    } else {
      message.info('未发现任何设备，请确保设备已连接并启用USB调试')
    }

  } catch (error) {
    console.error('扫描设备失败:', error)
    message.error('扫描设备失败: ' + (error as Error).message)
  } finally {
    isScanning.value = false
  }
}

// 映射设备状态
const mapDeviceStatus = (status: string): DeviceMode => {
  switch (status) {
    case 'device':
      return 'sys'
    case 'recovery':
      return 'rec'
    case 'fastboot':
      return 'fastboot'
    case 'fastbootd':
      return 'fastbootd'
    case 'sideload':
      return 'sideload'
    case 'unauthorized':
      return 'unauthorized'
    case 'offline':
      return 'offline'
    default:
      return 'unknown'
  }
}

// 获取设备状态类型
const getDeviceStatusType = (mode: DeviceMode, connected: boolean) => {
  if (!connected) return 'error'

  switch (mode) {
    case 'sys':
      return 'success'
    case 'rec':
    case 'fastboot':
    case 'fastbootd':
    case 'sideload':
      return 'warning'
    case 'unauthorized':
      return 'warning'
    case 'offline':
      return 'error'
    default:
      return 'default'
  }
}

// 获取设备状态文本
const getDeviceStatusText = (mode: DeviceMode, connected: boolean) => {
  if (!connected) return '离线'

  switch (mode) {
    case 'sys':
      return '在线'
    case 'rec':
      return 'Recovery'
    case 'fastboot':
      return 'Fastboot'
    case 'fastbootd':
      return 'Fastbootd'
    case 'sideload':
      return 'Sideload'
    case 'unauthorized':
      return '未授权'
    case 'offline':
      return '离线'
    default:
      return '未知'
  }
}

// 获取设备状态样式类
const getDeviceStatusClass = (mode: DeviceMode, connected: boolean) => {
  if (!connected) return 'device-status-offline'

  switch (mode) {
    case 'sys':
      return 'device-status-online'
    case 'unauthorized':
      return 'device-status-unauthorized'
    case 'offline':
      return 'device-status-offline'
    default:
      return 'device-status-online'
  }
}

const handleDeviceAction = async (key: string, device?: DeviceInfo) => {
  if (!device) {
    message.error('请选择设备')
    return
  }

  try {
    switch (key) {
      case 'reboot':
        await rebootDevice(device.serial)
        break
      case 'disconnect':
        message.info('断开连接功能开发中')
        break
      case 'info':
        await showDeviceInfo(device.serial)
        break
      case 'remove':
        removeDevice(device.serial)
        break
    }
  } catch (error) {
    console.error('设备操作失败:', error)
    message.error('操作失败: ' + (error as Error).message)
  }
}

// 重启设备
const rebootDevice = async (serial: string) => {
  try {
    const result = await window.go.main.App.RebootDevice(serial, 'system')
    if (result.success) {
      message.success('设备重启命令已发送')
      // 延迟重新扫描设备
      setTimeout(() => {
        scanDevices()
      }, 3000)
    } else {
      message.error('重启设备失败: ' + result.error)
    }
  } catch (error) {
    message.error('重启设备失败: ' + (error as Error).message)
  }
}

// 显示设备详细信息
const showDeviceInfo = async (serial: string) => {
  try {
    message.loading('正在获取设备详细信息...', { duration: 0 } as any)

    const deviceInfo = await window.go.main.App.GetDeviceInfo(serial)
    const properties = await window.go.main.App.GetDeviceProperties(serial)

    console.log('设备详细信息:', deviceInfo)
    console.log('设备属性:', properties)

    selectedDeviceDetail.value = deviceInfo
    selectedDeviceProperties.value = properties
    showDeviceDetailModal.value = true

    message.destroyAll()
    message.success('设备信息获取成功')
  } catch (error) {
    message.destroyAll()
    message.error('获取设备信息失败: ' + (error as Error).message)
  }
}

// 移除设备
const removeDevice = (serial: string) => {
  devices.value = devices.value.filter(device => device.serial !== serial)
  message.success('设备已从列表中移除')
}

// 检查单个设备连接状态
const checkDeviceConnection = async (serial: string) => {
  try {
    const connected = await window.go.main.App.CheckDeviceConnection(serial)
    const deviceIndex = devices.value.findIndex(device => device.serial === serial)

    if (deviceIndex !== -1) {
      devices.value[deviceIndex].connected = connected
      devices.value[deviceIndex].lastSeen = new Date()

      // 如果设备状态发生变化，更新模式
      if (connected) {
        devices.value[deviceIndex].mode = 'sys'
      } else {
        devices.value[deviceIndex].mode = 'offline'
      }
    }

    return connected
  } catch (error) {
    console.error(`检查设备 ${serial} 连接状态失败:`, error)
    return false
  }
}

// 批量检查所有设备连接状态
const checkAllDevicesConnection = async () => {
  if (devices.value.length === 0 || isScanning.value) {
    return
  }

  const connectionPromises = devices.value.map(device =>
    checkDeviceConnection(device.serial)
  )

  try {
    await Promise.all(connectionPromises)
  } catch (error) {
    console.error('批量检查设备连接状态失败:', error)
  }
}

// 自动刷新
const autoRefreshInterval = ref<ReturnType<typeof setInterval> | null>(null)
const autoRefreshEnabled = ref(true)

const startAutoRefresh = () => {
  if (autoRefreshInterval.value) {
    clearInterval(autoRefreshInterval.value)
  }

  if (autoRefreshEnabled.value) {
    autoRefreshInterval.value = setInterval(async () => {
      if (!isScanning.value) {
        // 如果有设备，先快速检查连接状态
        if (devices.value.length > 0) {
          await checkAllDevicesConnection()
        }

        // 每30秒进行一次完整扫描
        const now = Date.now()
        const lastFullScan = localStorage.getItem('hout-last-full-scan')
        const shouldFullScan = !lastFullScan || (now - parseInt(lastFullScan)) > 30000

        if (shouldFullScan) {
          localStorage.setItem('hout-last-full-scan', now.toString())
          scanDevices()
        }
      }
    }, 3000) // 每3秒检查一次
  }
}

const stopAutoRefresh = () => {
  if (autoRefreshInterval.value) {
    clearInterval(autoRefreshInterval.value)
    autoRefreshInterval.value = null
  }
}

const toggleAutoRefresh = () => {
  autoRefreshEnabled.value = !autoRefreshEnabled.value
  if (autoRefreshEnabled.value) {
    startAutoRefresh()
    message.success('已启用自动刷新')
  } else {
    stopAutoRefresh()
    message.info('已禁用自动刷新')
  }
}

// 生命周期
onMounted(() => {
  // 自动扫描设备
  scanDevices()
  // 启动自动刷新
  startAutoRefresh()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.device-info-panel {
  height: 100%;
}

.device-list {
  margin-top: 16px;
}

.device-card {
  transition: all 0.3s ease;
}

.device-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.device-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.loading-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
  margin: 60px 0;
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  display: inline-block;
  margin-right: 4px;
}

/* 设备状态指示器样式 */
.device-status-online .status-indicator {
  background-color: var(--hout-success-color);
  animation: pulse 2s infinite;
}

.device-status-offline .status-indicator {
  background-color: var(--hout-error-color);
}

.device-status-unauthorized .status-indicator {
  background-color: var(--hout-warning-color);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
