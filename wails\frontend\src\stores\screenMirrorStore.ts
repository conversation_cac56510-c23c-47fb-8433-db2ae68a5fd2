import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'

// 简化的类型定义
interface ScreenMirrorSession {
  id: string
  deviceId: string
  startTime: Date
  endTime?: Date
  status: 'active' | 'stopped' | 'error'
}

interface ScreenMirrorDevice {
  id: string
  name: string
  model: string
  isSupported: boolean
  isConnected: boolean
}

interface ScreenMirrorConfig {
  quality: 'low' | 'medium' | 'high'
  fps: number
  bitrate: number
  resolution: string
}

const DEFAULT_CONFIG: ScreenMirrorConfig = {
  quality: 'medium',
  fps: 30,
  bitrate: 2000,
  resolution: '1280x720'
}

export const useScreenMirrorStore = defineStore('screenMirror', () => {
  // 状态
  const currentSession = ref<ScreenMirrorSession | null>(null)
  const sessions = ref<ScreenMirrorSession[]>([])
  const supportedDevices = ref<ScreenMirrorDevice[]>([])
  const selectedDevice = ref<ScreenMirrorDevice | null>(null)
  const config = reactive<ScreenMirrorConfig>({ ...DEFAULT_CONFIG })
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const showSettings = ref(false)
  const isFullscreen = ref(false)

  // 动作
  const setCurrentSession = (session: ScreenMirrorSession | null) => {
    currentSession.value = session
  }

  const addSession = (session: ScreenMirrorSession) => {
    sessions.value.push(session)
  }

  const removeSession = (sessionId: string) => {
    const index = sessions.value.findIndex(s => s.id === sessionId)
    if (index > -1) {
      sessions.value.splice(index, 1)
    }
  }

  const setSupportedDevices = (devices: ScreenMirrorDevice[]) => {
    supportedDevices.value = devices
  }

  const setSelectedDevice = (device: ScreenMirrorDevice | null) => {
    selectedDevice.value = device
  }

  const updateConfig = (updates: Partial<ScreenMirrorConfig>) => {
    Object.assign(config, updates)
  }

  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  const setError = (err: string | null) => {
    error.value = err
  }

  const toggleSettings = () => {
    showSettings.value = !showSettings.value
  }

  const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value
  }

  const startMirror = async (deviceId: string) => {
    setLoading(true)
    setError(null)
    
    try {
      const session: ScreenMirrorSession = {
        id: Date.now().toString(),
        deviceId,
        startTime: new Date(),
        status: 'active'
      }
      
      setCurrentSession(session)
      addSession(session)
      
      // 模拟启动投屏
      await new Promise(resolve => setTimeout(resolve, 2000))
      
    } catch (err) {
      setError(err instanceof Error ? err.message : '启动投屏失败')
    } finally {
      setLoading(false)
    }
  }

  const stopMirror = () => {
    if (currentSession.value) {
      currentSession.value.status = 'stopped'
      currentSession.value.endTime = new Date()
      setCurrentSession(null)
    }
  }

  const clearSessions = () => {
    sessions.value = []
  }

  return {
    // 状态
    currentSession,
    sessions,
    supportedDevices,
    selectedDevice,
    config,
    isLoading,
    error,
    showSettings,
    isFullscreen,
    
    // 动作
    setCurrentSession,
    addSession,
    removeSession,
    setSupportedDevices,
    setSelectedDevice,
    updateConfig,
    setLoading,
    setError,
    toggleSettings,
    toggleFullscreen,
    startMirror,
    stopMirror,
    clearSessions
  }
})
