<template>
  <div class="screen-mirror-panel">
    <n-card title="屏幕镜像" :bordered="false">
      <n-result status="info" title="屏幕镜像" description="此功能正在开发中...">
        <template #icon>
          <n-icon size="64" color="#18a058">
            <Desktop />
          </n-icon>
        </template>
      </n-result>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { NCard, NResult, NIcon } from 'naive-ui'
import { Desktop } from '@vicons/ionicons5'
</script>

<style scoped>
.screen-mirror-panel {
  height: 100%;
}
</style>
