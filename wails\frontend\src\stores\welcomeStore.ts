import { defineStore } from 'pinia'
import { ref, reactive } from 'vue'

// 简化的类型定义
enum WelcomeStep {
  WELCOME = 'welcome',
  ACTIVATION = 'activation',
  COMPLETE = 'complete'
}

enum ActivationStatus {
  NOT_ACTIVATED = 'not_activated',
  ACTIVATED = 'activated',
  EXPIRED = 'expired'
}

interface UserConfiguration {
  username: string
  email: string
  autoStart: boolean
  enableNotifications: boolean
}

interface AppConfig {
  isActivated: boolean
  activationStatus: ActivationStatus
  activationDate?: Date
  expiryDate?: Date
  userConfig: UserConfiguration
}

const DEFAULT_USER_CONFIG: UserConfiguration = {
  username: '',
  email: '',
  autoStart: false,
  enableNotifications: true
}

const DEFAULT_APP_CONFIG: AppConfig = {
  isActivated: false,
  activationStatus: ActivationStatus.NOT_ACTIVATED,
  userConfig: { ...DEFAULT_USER_CONFIG }
}

// 欢迎页面状态管理
export const useWelcomeStore = defineStore('welcome', () => {
  // 状态
  const currentStep = ref<WelcomeStep>(WelcomeStep.WELCOME)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const userConfig = reactive<UserConfiguration>({ ...DEFAULT_USER_CONFIG })
  const activationCode = ref('')
  const activationStatus = ref<ActivationStatus>(ActivationStatus.NOT_ACTIVATED)

  // 动作
  const setCurrentStep = (step: WelcomeStep) => {
    currentStep.value = step
  }

  const setLoading = (loading: boolean) => {
    isLoading.value = loading
  }

  const setError = (err: string | null) => {
    error.value = err
  }

  const setUserConfig = (config: Partial<UserConfiguration>) => {
    Object.assign(userConfig, config)
  }

  const setActivationCode = (code: string) => {
    activationCode.value = code
  }

  const setActivationStatus = (status: ActivationStatus) => {
    activationStatus.value = status
  }

  const nextStep = () => {
    if (!canProceedToNext()) return

    const stepOrder = [
      WelcomeStep.WELCOME,
      WelcomeStep.ACTIVATION,
      WelcomeStep.COMPLETE
    ]

    const currentIndex = stepOrder.indexOf(currentStep.value)
    if (currentIndex < stepOrder.length - 1) {
      currentStep.value = stepOrder[currentIndex + 1]
    }
  }

  const previousStep = () => {
    const stepOrder = [
      WelcomeStep.WELCOME,
      WelcomeStep.ACTIVATION,
      WelcomeStep.COMPLETE
    ]

    const currentIndex = stepOrder.indexOf(currentStep.value)
    if (currentIndex > 0) {
      currentStep.value = stepOrder[currentIndex - 1]
    }
  }

  const resetWelcome = () => {
    currentStep.value = WelcomeStep.WELCOME
    isLoading.value = false
    error.value = null
    Object.assign(userConfig, DEFAULT_USER_CONFIG)
    activationCode.value = ''
    activationStatus.value = ActivationStatus.NOT_ACTIVATED
  }

  const validateCurrentStep = (): boolean => {
    switch (currentStep.value) {
      case WelcomeStep.WELCOME:
        return true
      case WelcomeStep.ACTIVATION:
        return activationStatus.value === ActivationStatus.ACTIVATED ||
               !!(activationCode.value && activationCode.value.trim().length > 0)
      case WelcomeStep.COMPLETE:
        return true
      default:
        return false
    }
  }

  const canProceedToNext = (): boolean => {
    return validateCurrentStep()
  }

  return {
    // 状态
    currentStep,
    isLoading,
    error,
    userConfig,
    activationCode,
    activationStatus,
    
    // 动作
    setCurrentStep,
    setLoading,
    setError,
    setUserConfig,
    setActivationCode,
    setActivationStatus,
    nextStep,
    previousStep,
    resetWelcome,
    validateCurrentStep,
    canProceedToNext
  }
})

// 应用配置状态管理
export const useAppConfigStore = defineStore('appConfig', () => {
  // 状态
  const config = reactive<AppConfig>({ ...DEFAULT_APP_CONFIG })

  // 动作
  const setConfig = (newConfig: Partial<AppConfig>) => {
    Object.assign(config, newConfig)
    // 保存到localStorage
    localStorage.setItem('hout-app-config', JSON.stringify(config))
  }

  const setActivated = (activated: boolean) => {
    config.isActivated = activated
    config.activationStatus = activated ? ActivationStatus.ACTIVATED : ActivationStatus.NOT_ACTIVATED
    config.activationDate = activated ? new Date() : undefined
    
    // 保存到localStorage
    localStorage.setItem('hout-app-config', JSON.stringify(config))
  }

  const setUserConfig = (userConfig: UserConfiguration) => {
    config.userConfig = userConfig
    localStorage.setItem('hout-app-config', JSON.stringify(config))
  }

  const isActivated = (): boolean => {
    if (!config.isActivated || config.activationStatus !== ActivationStatus.ACTIVATED) {
      return false
    }

    if (isExpired()) {
      return false
    }

    return true
  }

  const needsActivation = (): boolean => {
    if (!config.isActivated || config.activationStatus !== ActivationStatus.ACTIVATED) {
      return true
    }

    if (isExpired()) {
      return true
    }

    return false
  }

  const isExpired = (): boolean => {
    if (!config.expiryDate) return false
    return new Date() > new Date(config.expiryDate)
  }

  const resetConfig = () => {
    Object.assign(config, DEFAULT_APP_CONFIG)
    localStorage.removeItem('hout-app-config')
  }

  // 初始化时从localStorage读取配置
  const initConfig = () => {
    const savedConfig = localStorage.getItem('hout-app-config')
    if (savedConfig) {
      try {
        const parsedConfig = JSON.parse(savedConfig)
        Object.assign(config, parsedConfig)
      } catch (e) {
        console.warn('Failed to parse saved app config:', e)
      }
    }
  }

  // 初始化
  initConfig()

  return {
    // 状态
    config,
    
    // 动作
    setConfig,
    setActivated,
    setUserConfig,
    isActivated,
    needsActivation,
    isExpired,
    resetConfig,
    initConfig
  }
})

// 导出类型
export { WelcomeStep, ActivationStatus, type UserConfiguration, type AppConfig }
