import { defineStore } from 'pinia'
import { ref } from 'vue'
import { DeviceInfo } from "../types/device";

export const useDeviceStore = defineStore('device', () => {
  // 状态
  const devices = ref<DeviceInfo[]>([])
  const selectedDevice = ref<DeviceInfo | undefined>(undefined)
  const isScanning = ref(false)
  const lastUpdate = ref(new Date())

  // 动作
  const setDevices = (newDevices: DeviceInfo[]) => {
    devices.value = newDevices
    lastUpdate.value = new Date()

    // 如果当前选中的设备不在新列表中，清除选择
    if (selectedDevice.value &&
        !newDevices.find(d => d.serial === selectedDevice.value?.serial)) {
      selectedDevice.value = undefined
    }
  }

  const selectDevice = (device: DeviceInfo | undefined) => {
    selectedDevice.value = device
  }

  const updateDevice = (serial: string, updates: Partial<DeviceInfo>) => {
    const deviceIndex = devices.value.findIndex(d => d.serial === serial)
    if (deviceIndex > -1) {
      devices.value[deviceIndex] = { ...devices.value[deviceIndex], ...updates }
    }

    if (selectedDevice.value?.serial === serial) {
      selectedDevice.value = { ...selectedDevice.value, ...updates }
    }

    lastUpdate.value = new Date()
  }

  const setScanning = (scanning: boolean) => {
    isScanning.value = scanning
  }

  const clearDevices = () => {
    devices.value = []
    selectedDevice.value = undefined
    lastUpdate.value = new Date()
  }

  return {
    devices,
    selectedDevice,
    isScanning,
    lastUpdate,
    setDevices,
    selectDevice,
    updateDevice,
    setScanning,
    clearDevices
  }
})
