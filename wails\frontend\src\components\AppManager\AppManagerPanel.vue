<template>
  <div class="app-manager-panel">
    <n-card title="应用管理" :bordered="false">
      <n-result status="info" title="应用管理" description="此功能正在开发中...">
        <template #icon>
          <n-icon size="64" color="#18a058">
            <Apps />
          </n-icon>
        </template>
      </n-result>
    </n-card>
  </div>
</template>

<script setup lang="ts">
import { NCard, NResult, NIcon } from 'naive-ui'
import { Apps } from '@vicons/ionicons5'
</script>

<style scoped>
.app-manager-panel {
  height: 100%;
}
</style>
