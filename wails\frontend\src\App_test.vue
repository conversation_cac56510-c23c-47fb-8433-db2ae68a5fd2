<template>
  <div style="padding: 20px; font-family: Arial, sans-serif;">
    <h1 style="color: #333;">HOUT Vue 3 测试</h1>
    <p>如果您能看到这个页面，说明Vue 3应用正在正常运行！</p>
    <button @click="count++" style="padding: 10px 20px; margin: 10px; background: #007acc; color: white; border: none; border-radius: 4px;">
      点击次数: {{ count }}
    </button>
    <div style="margin-top: 20px;">
      <p>当前时间: {{ currentTime }}</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

const count = ref(0)
const currentTime = ref('')

let timer: ReturnType<typeof setInterval> | null = null

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

onMounted(() => {
  updateTime()
  timer = setInterval(updateTime, 1000)
  console.log('Vue 3 测试应用已挂载')
})

onUnmounted(() => {
  if (timer) {
    clearInterval(timer)
  }
})
</script>
